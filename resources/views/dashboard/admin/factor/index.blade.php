@extends('layouts.dashboard')

@section('title', ' - ایجاد فاکتور جدید')

@section('content')

    @php
        $data = Cache::get('gold_prices', []);
    @endphp
    <main class="relative w-full md:p-6">
        <div class="container grid grid-cols-1 gap-3 pb-60 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-12">
            <div class="relative h-auto min-h-96 w-full rounded-xl bg-white p-5 shadow-xl dark:bg-gray-900 md:col-span-10">
                <div x-data="{ lockButtonGold18k: true }">
                    <livewire:dashboard.admin.factor.header />
                    <livewire:dashboard.admin.order.factor.index />

                </div>
            </div>

        </div>

    </main>

    <div
        class="fixed left-0 top-0 z-[1001] flex h-screen w-screen items-start justify-center overflow-y-auto bg-gray-900/75 backdrop-blur-sm"
        x-transition:enter="transition ease-out duration-300"
        x-transition:enter-start="opacity-0"
        x-transition:enter-end="opacity-100"
        x-transition:leave="transition ease-in duration-300"
        x-transition:leave-start="opacity-100"
        x-transition:leave-end="opacity-0"
        x-init="$el.addEventListener('close-subscribe-modal', () => {
            getSubscribeModal = false;
        });"
        x-show="getSubscribeModal"
        x-cloak
    >
        <div
            class="fade-scale relative top-10 w-full max-w-2xl rounded-xl bg-gray-900 p-5 shadow-xl"
            x-show="getSubscribeModal"
            x-transition:enter="transition ease-out duration-100"
            x-transition:enter-start="opacity-0 scale-75"
            x-transition:enter-end="opacity-100 scale-100"
            x-transition:leave="transition ease-in duration-100"
            x-transition:leave-start="opacity-100 scale-100"
            x-transition:leave-end="opacity-0 scale-75"
        >
            <div>
                <div class="mb-3 flex justify-between p-3 pb-0 pt-0 dark:border-gray-800">
                    <div>
                        <h1 class="text-base text-gray-200 md:text-xl">جستجو در بین مشتریان</h1>
                    </div>
                    <button
                        class="mr-auto inline-flex h-8 w-8 items-center rounded-lg bg-transparent p-1.5 text-sm text-gray-500 transition-all hover:bg-gray-200 hover:text-gray-700 dark:hover:bg-gray-600 dark:hover:text-white"
                        type="button"
                        @click="getSubscribeModal = false, lock = false"
                    >
                        <svg
                            class="h-5 w-5"
                            aria-hidden="true"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                fill-rule="evenodd"
                                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                clip-rule="evenodd"
                            ></path>
                        </svg>
                        <span class="sr-only">Close modal</span>
                    </button>
                </div>
                <livewire:dashboard.admin.subscribers.find-user-modal lazy="on-load" />

            </div>
        </div>
    </div>

@stop

@push('script')
    <script>
        function Comma(Num) {
            Num = Num.toString().replace(/,/g, '');
            if (isNaN(Num) || Num === '') {
                return '';
            }
            let negative = Num[0] === '-' ? '-' : '';
            Num = Num.replace('-', '');
            let parts = Num.split('.');
            let integerPart = parts[0];
            let decimalPart = parts.length > 1 ? '.' + parts[1] : '';
            let rgx = /(\d+)(\d{3})/;
            while (rgx.test(integerPart)) {
                integerPart = integerPart.replace(rgx, '$1' + ',' + '$2');
            }
            return negative + integerPart + decimalPart;
        }
    </script>
@endpush

<div
    class="fade-in absolute left-0 top-0 z-[9999] h-screen w-screen bg-white/75 backdrop-blur-md"
    x-cloak
    :class="settingDrawerLeft == false ? 'hidden' : ''"
    @click="settingDrawerLeft = false; lock = false"
>
</div>
<div x-data="{
    tab: window.location.hash ? window.location.hash.replace('#', '') : 'first-setting',
    setTab(newTab) {
        this.tab = newTab;
        window.location.hash = newTab;
    },
    createDiscountModal: false,
    editDiscountModal: false
}">
    <div
        class="fixed top-0 z-[10000] h-screen transform overflow-y-auto overflow-x-hidden rounded-b-none rounded-t-xl bg-white p-5 pb-10 pt-0 text-white shadow-md transition-all duration-300 max-md:w-screen md:top-2 md:h-[98vh] md:w-[800px]"
        x-cloak
        :class="settingDrawerLeft == true ? 'md:left-4 left-0 ' : '-translate-x-full left-0'"
    >
        <div class="mb-3 flex items-start justify-between pt-3 md:pt-5">
            <div>
                <a
                    class="text-xl font-bold text-gray-700"
                    href="/"
                >تنظیمات سایت</a>

            </div>
            <button
                class="hover: mr-auto inline-flex h-8 w-8 items-center rounded-lg bg-transparent p-1.5 text-sm text-gray-500 transition-all hover:bg-gray-200 dark:hover:bg-gray-600 dark:hover:text-white"
                type="button"
                @click="settingDrawerLeft = false;lock = false"
            >
                <svg
                    class="h-5 w-5"
                    aria-hidden="true"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        fill-rule="evenodd"
                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                        clip-rule="evenodd"
                    ></path>
                </svg>
                <span class="sr-only">Close modal</span>
            </button>
        </div>
        <div
            class="text-gray-700"
            @if (auth()->user()->level == 'admin') x-data="{ selectTab: 'setting' }"
            @else x-data="{ selectTab: 'gate' }" @endif
        >
            <div class="top-0 z-50 w-full bg-white md:sticky">
                <div class="mb-4 flex items-center gap-8 overflow-x-auto">
                    @if (auth()->user()->level == 'admin')
                        <button
                            class="py-3 text-sm text-gray-700"
                            type="button"
                            @click="selectTab = 'setting'"
                            :class="selectTab == 'setting' ? 'border-b-2 border-red-500 font-bold' : 'border-b border-gray-100'"
                        >
                            <span class="flex items-center gap-2">
                                <svg
                                    class="size-6 text-gray-600"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75"
                                    />
                                </svg>

                                <span class="whitespace-nowrap text-sm">تنظیمات اولیه</span>
                            </span>
                        </button>
                    @endif
                    <button
                        class="py-3 text-sm text-gray-700"
                        type="button"
                        @click="selectTab = 'gate'"
                        :class="selectTab == 'gate' ? 'border-b-2 border-red-500 font-bold' : 'border-b border-gray-100'"
                    >
                        <span class="flex items-center gap-2">

                            <svg
                                class="size-6 text-gray-600"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M15.75 15.75V18m-7.5-6.75h.008v.008H8.25v-.008Zm0 2.25h.008v.008H8.25V13.5Zm0 2.25h.008v.008H8.25v-.008Zm0 2.25h.008v.008H8.25V18Zm2.498-6.75h.007v.008h-.007v-.008Zm0 2.25h.007v.008h-.007V13.5Zm0 2.25h.007v.008h-.007v-.008Zm0 2.25h.007v.008h-.007V18Zm2.504-6.75h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V13.5Zm0 2.25h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V18Zm2.498-6.75h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V13.5ZM8.25 6h7.5v2.25h-7.5V6ZM12 2.25c-1.892 0-3.758.11-5.593.322C5.307 2.7 4.5 3.65 4.5 4.757V19.5a2.25 2.25 0 0 0 2.25 2.25h10.5a2.25 2.25 0 0 0 2.25-2.25V4.757c0-1.108-.806-2.057-1.907-2.185A48.507 48.507 0 0 0 12 2.25Z"
                                />
                            </svg>

                            <span class="whitespace-nowrap text-sm">محاسبات تیدامد</span>
                        </span>
                    </button>
                    @if (auth()->user()->level == 'admin')
                        <button
                            class="py-3 text-sm text-gray-700"
                            type="button"
                            @click="selectTab = 'discount'"
                            :class="selectTab == 'discount' ? 'border-b-2 border-red-500 font-bold' : 'border-b border-gray-100'"
                        >
                            <span class="flex items-center gap-2">

                                <svg
                                    class="size-6 text-gray-600"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M14.25 6.087c0-.355.186-.676.401-.959.221-.29.349-.634.349-1.003 0-1.036-1.007-1.875-2.25-1.875s-2.25.84-2.25 1.875c0 .369.128.713.349 1.003.215.283.401.604.401.959v0a.64.64 0 0 1-.657.643 48.39 48.39 0 0 1-4.163-.3c.186 1.613.293 3.25.315 4.907a.656.656 0 0 1-.658.663v0c-.355 0-.676-.186-.959-.401a1.647 1.647 0 0 0-1.003-.349c-1.036 0-1.875 1.007-1.875 2.25s.84 2.25 1.875 2.25c.369 0 .713-.128 1.003-.349.283-.215.604-.401.959-.401v0c.31 0 .555.26.532.57a48.039 48.039 0 0 1-.642 5.056c1.518.19 3.058.309 4.616.354a.64.64 0 0 0 .657-.643v0c0-.355-.186-.676-.401-.959a1.647 1.647 0 0 1-.349-1.003c0-1.035 1.008-1.875 2.25-1.875 1.243 0 2.25.84 2.25 1.875 0 .369-.128.713-.349 1.003-.215.283-.4.604-.4.959v0c0 .333.277.599.61.58a48.1 48.1 0 0 0 5.427-.63 48.05 48.05 0 0 0 .582-4.717.532.532 0 0 0-.533-.57v0c-.355 0-.676.186-.959.401-.29.221-.634.349-1.003.349-1.035 0-1.875-1.007-1.875-2.25s.84-2.25 1.875-2.25c.37 0 .713.128 1.003.349.283.215.604.401.96.401v0a.656.656 0 0 0 .658-.663 48.422 48.422 0 0 0-.37-5.36c-1.886.342-3.81.574-5.766.689a.578.578 0 0 1-.61-.58v0Z"
                                    />
                                </svg>

                                <span class="whitespace-nowrap text-sm">کدهای تخفیف</span>
                            </span>
                        </button>
                    @endif
                    @if (auth()->user()->level == 'admin')
                        <button
                            class="py-3 text-sm text-gray-700"
                            type="button"
                            @click="selectTab = 'var'"
                            :class="selectTab == 'var' ? 'border-b-2 border-red-500 font-bold' : 'border-b border-gray-100'"
                        >
                            <span class="flex items-center gap-2">

                                <svg
                                    class="size-6 text-gray-600"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M2.25 12.75V12A2.25 2.25 0 0 1 4.5 9.75h15A2.25 2.25 0 0 1 21.75 12v.75m-8.69-6.44-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9a2.25 2.25 0 0 0-2.25-2.25h-5.379a1.5 1.5 0 0 1-1.06-.44Z"
                                    />
                                </svg>

                                <span class="whitespace-nowrap text-sm">متغییرها</span>
                            </span>
                        </button>
                        <button
                            class="py-3 text-sm text-gray-700"
                            type="button"
                            @click="selectTab = 'other'"
                            :class="selectTab == 'other' ? 'border-b-2 border-red-500 font-bold' : 'border-b border-gray-100'"
                        >
                            <span class="flex items-center gap-2">

                                <svg
                                    class="size-6 text-gray-600"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z"
                                    />
                                </svg>

                                <span class="whitespace-nowrap text-sm">سایر تنظیمات</span>
                            </span>
                        </button>
                    @endif
                </div>
            </div>
            <div x-show="selectTab == 'setting'">
                <livewire:dashboard.admin.setting.first-setting />
            </div>
            <div x-show="selectTab == 'gate'">
                <livewire:dashboard.admin.setting.calculator />
            </div>
            <div x-show="selectTab == 'discount'">
                <livewire:dashboard.admin.setting.discount />
                <livewire:dashboard.admin.setting.discount.index />
            </div>
            <div x-show="selectTab == 'var'">
                {{-- <livewire:dashboard.admin.setting.other lazy /> --}}
            </div>
            <div x-show="selectTab == 'other'">
                <livewire:dashboard.admin.setting.other lazy />
            </div>
        </div>
        <div
            class="fixed left-0 top-0 z-[1005] flex h-full w-full items-start justify-center overflow-y-auto bg-gray-900/75"
            x-transition:enter="transition ease-out duration-100"
            x-transition:enter-start="opacity-0"
            x-transition:enter-end="opacity-100"
            x-transition:leave="transition ease-in duration-300"
            x-transition:leave-start="opacity-100"
            x-transition:leave-end="opacity-0"
            x-show="editDiscountModal"
            x-cloak
        >
            <div
                class="fade-scale relative top-10 h-auto w-full max-w-xl rounded-xl bg-white p-5 shadow-xl dark:bg-gray-900"
                x-show="editDiscountModal"
                x-transition:enter="transition ease-out duration-100"
                x-transition:enter-start="opacity-0 scale-75"
                x-transition:enter-end="opacity-100 scale-100"
                x-transition:leave="transition ease-in duration-100"
                x-transition:leave-start="opacity-100 scale-100"
                x-transition:leave-end="opacity-0 scale-75"
            >
                <div>

                    <div class="mb-3 flex justify-between p-3 pb-0 pt-0 dark:border-gray-800">
                        <div>
                            <h1 class="text-base font-bold text-gray-700 dark:text-gray-200 md:text-xl">ویرایش کدتخفیف
                            </h1>
                        </div>
                        <button
                            class="mr-auto inline-flex h-8 w-8 items-center rounded-lg bg-transparent p-1.5 text-sm text-gray-500 transition-all hover:bg-gray-200 hover:text-gray-700 dark:hover:bg-gray-600 dark:hover:text-white"
                            type="button"
                            @click="editDiscountModal = false, lock = false"
                        >
                            <svg
                                class="h-5 w-5"
                                aria-hidden="true"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    fill-rule="evenodd"
                                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                    clip-rule="evenodd"
                                ></path>
                            </svg>
                            <span class="sr-only">Close modal</span>
                        </button>
                    </div>
                    <livewire:dashboard.admin.setting.discount.show-discount />
                </div>
            </div>
        </div>

        <div
            class="fixed left-0 top-0 z-[1005] flex h-full w-full items-start justify-center overflow-y-auto bg-gray-900/75"
            x-transition:enter="transition ease-out duration-100"
            x-transition:enter-start="opacity-0"
            x-transition:enter-end="opacity-100"
            x-transition:leave="transition ease-in duration-300"
            x-transition:leave-start="opacity-100"
            x-transition:leave-end="opacity-0"
            x-show="createDiscountModal"
            x-cloak
        >
            <div
                class="fade-scale relative top-10 h-auto w-full max-w-xl rounded-xl bg-white p-5 shadow-xl dark:bg-gray-900"
                x-show="createDiscountModal"
                x-transition:enter="transition ease-out duration-100"
                x-transition:enter-start="opacity-0 scale-75"
                x-transition:enter-end="opacity-100 scale-100"
                x-transition:leave="transition ease-in duration-100"
                x-transition:leave-start="opacity-100 scale-100"
                x-transition:leave-end="opacity-0 scale-75"
            >
                <div>

                    <div class="mb-3 flex justify-between p-3 pb-0 pt-0 dark:border-gray-800">
                        <div>
                            <h1 class="text-base font-bold text-gray-700 dark:text-gray-200 md:text-xl">ایجاد کدتخفیف
                            </h1>
                        </div>
                        <button
                            class="mr-auto inline-flex h-8 w-8 items-center rounded-lg bg-transparent p-1.5 text-sm text-gray-500 transition-all hover:bg-gray-200 hover:text-gray-700 dark:hover:bg-gray-600 dark:hover:text-white"
                            type="button"
                            @click="createDiscountModal = false, lock = false"
                        >
                            <svg
                                class="h-5 w-5"
                                aria-hidden="true"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    fill-rule="evenodd"
                                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                    clip-rule="evenodd"
                                ></path>
                            </svg>
                            <span class="sr-only">Close modal</span>
                        </button>
                    </div>
                    <livewire:dashboard.admin.setting.discount.create-discount />
                </div>
            </div>
        </div>
        @push('scripts')
            <script>
                function Comma(Num) {
                    Num = Num.toString().replace(/,/g, '');
                    if (isNaN(Num) || Num === '') {
                        return '';
                    }
                    let negative = Num[0] === '-' ? '-' : '';
                    Num = Num.replace('-', '');
                    let parts = Num.split('.');
                    let integerPart = parts[0];
                    let decimalPart = parts.length > 1 ? '.' + parts[1] : '';
                    let rgx = /(\d+)(\d{3})/;
                    while (rgx.test(integerPart)) {
                        integerPart = integerPart.replace(rgx, '$1' + ',' + '$2');
                    }
                    return negative + integerPart + decimalPart;
                }
            </script>
            <script>
                document.addEventListener("DOMContentLoaded", function() {
                    jalaliDatepicker.startWatch({
                        time: true, // فعال‌سازی انتخاب ساعت
                        hasSecond: true, // نمایش ثانیه
                        hideAfterChange: true,
                        persianDigits: false, // اعداد انگلیسی
                        autoShow: true,
                        autoHide: true
                    });
                });
            </script>
        @endpush
    </div>

</div>

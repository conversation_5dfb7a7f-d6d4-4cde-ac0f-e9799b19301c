<form
    class="fade-in-horiz relative mt-6"
    wire:submit="save"
    x-data="{ gold18k: @entangle('data.gold18k_status') }"
>
    @include('layouts.tools.loading')
    <div class="grid grid-cols-2 gap-3">
        <div
            class="max-md:col-span-2"
            wire:ignore
        >
            <label
                class="block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="gold18k"
            >نرخ محاسبه طلا:</label>
            <div class="relative">
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 disabled:bg-gray-200 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="gold18k"
                    type="text"
                    :disabled="gold18k"
                    onkeyup="javascript:this.value=Comma(this.value);"
                    wire:model="data.gold18k"
                >
            </div>
            <div class="mt-3 max-md:col-span-2">
                <label class="inline-flex cursor-pointer items-center">
                    <input
                        class="peer sr-only"
                        type="checkbox"
                        x-model="gold18k"
                        @click="gold18k = !gold18k"
                        wire:click="loadGold"
                    >
                    <div
                        class="peer relative h-6 w-11 rounded-full bg-gray-200 after:absolute after:start-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-red-500 peer-checked:after:-translate-x-full peer-checked:after:border-white peer-focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:peer-focus:ring-red-800 rtl:peer-checked:after:translate-x-full">
                    </div>
                    <span class="ms-3 text-sm font-bold text-gray-900 dark:font-normal dark:text-white">محاسبه نرخ طلا
                        بصورت آنلاین</span>
                </label>
            </div>
        </div>
        <div>
            <label
                class="block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="gold18kup"
            >مبلغ افزایش نرخ طلا:</label>
            <div class="relative">
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="gold18kup"
                    type="text"
                    onkeyup="javascript:this.value=Comma(this.value);"
                    wire:model="data.gold18kup"
                >
            </div>
        </div>

    </div>
    <div class="mt-3 grid grid-cols-2 gap-3 md:grid-cols-4">
        <div>
            <label
                class="block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="profit"
            >سود فروش <span class="rounded-xl p-0.5 px-2 text-xs text-gray-600">(درصد)</span>:</label>
            <div class="relative">
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="profit"
                    type="text"
                    wire:model="data.profit"
                >
            </div>
        </div>

        <div>
            <label
                class="block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="construction_wages"
            >اجرت ساخت <span class="rounded-xl p-0.5 px-2 text-xs text-gray-600">(درصد)</span>:</label>
            <div class="relative">
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="construction_wages"
                    type="text"
                    wire:model="data.construction_wages"
                >
            </div>
        </div>
        <div>
            <label
                class="block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="tax"
            >مالیات<span class="rounded-xl p-0.5 px-2 text-xs text-gray-600">(درصد)</span>:</label>
            <div class="relative">
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="tax"
                    type="text"
                    wire:model="data.tax"
                >
            </div>
        </div>
        <div class="col-span-3">
            <label
                class="block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="profit_offer"
            >سود فروش با تخفیف <span class="rounded-xl p-0.5 px-2 text-xs text-gray-600">(در صورتی که میخواهید این سود
                    روی همه محصولات اعمال شود)</span>:</label>
            <div class="relative">
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="profit_offer"
                    type="text"
                    wire:model="data.profit_offer"
                >
            </div>
        </div>
        <div>
            <label
                class="block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="weight_greater_than"
            >محاسبه تحفیف از چند گرم؟:</label>
            <div class="relative">
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="weight_greater_than"
                    type="text"
                    wire:model="data.weight_greater_than"
                >
            </div>
        </div>
        <div>
            <label
                class="block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="factorId"
            >شمارش فاکتور از:</label>
            <div class="relative">
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="factorId"
                    type="text"
                    wire:model="data.factorId"
                >
            </div>
        </div>
        <div>
            <label
                class="block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="idleTimeout"
            >زمان بیکاری <span class="rounded-xl p-0.5 px-2 text-xs text-gray-600">(ثانیه)</span>:</label>
            <div class="relative">
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="idleTimeout"
                    type="text"
                    wire:model="data.idleTimeout"
                >
            </div>
        </div>
        <div>
            <label
                class="block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="countdownTime"
            >شمارش زمان <span class="rounded-xl p-0.5 px-2 text-xs text-gray-600">(ثانیه)</span>:</label>
            <div class="relative">
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="countdownTime"
                    type="text"
                    wire:model="data.countdownTime"
                >
            </div>
        </div>
        <div>
            <label
                class="block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="popupTime"
            >نمایش پاپ آپ <span class="rounded-xl p-0.5 px-2 text-xs text-gray-600">(ثانیه)</span>:</label>
            <div class="relative">
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="popupTime"
                    type="text"
                    wire:model="data.popupTime"
                >
            </div>
        </div>

    </div>
    <div class="mt-4">
        <div class="grid grid-cols-1 gap-3 border-t border-gray-300 pt-4 md:grid-cols-4">
            <div class="md:col-span-2">
                <label
                    class="block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                    for="expireTimeLink"
                >اعتبار پرداخت لینک تا (بصورت دقیقه):</label>
                <div class="relative">
                    <input
                        class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="expireTimeLink"
                        type="text"
                        wire:model="data.expireTimeLink"
                    >
                </div>
            </div>
            <div>
                <label
                    class="block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                    for="post_price"
                >هزینه ارسال پست <span class="rounded-xl p-0.5 px-2 text-xs text-gray-600">(ریال)</span>:</label>
                <div class="relative">
                    <input
                        class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="post_price"
                        type="text"
                        onkeyup="javascript:this.value=Comma(this.value);"
                        wire:model="data.post_price"
                    >
                </div>
            </div>

        </div>
    </div>
    <div class="my-4 pb-4">
        <div class="grid grid-cols-1 gap-3 border-t border-gray-300 pt-4 md:grid-cols-4">
            <div class="md:col-span-2">
                <label
                    class="block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                    for="card_number"
                >شماره کارت واریزی<span class="rounded-xl p-0.5 px-2 text-xs text-gray-600">(محصولات بدون اجرت)
                    </span>:</label>
                <div class="relative">
                    <input
                        class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="card_number"
                        type="text"
                        wire:model="data.card_number"
                    >
                </div>
            </div>
            <div class="md:col-span-2">
                <label
                    class="block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                    for="sheba_card_number"
                >شبا کارت واریزی<span class="rounded-xl p-0.5 px-2 text-xs text-gray-600">(محصولات بدون اجرت)
                    </span>:</label>
                <div class="relative">
                    <input
                        class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="sheba_card_number"
                        type="text"
                        wire:model="data.sheba_card_number"
                    >
                </div>
            </div>
            <div class="md:col-span-2">
                <label
                    class="block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                    for="fullname_card_number"
                >به نام<span class="rounded-xl p-0.5 px-2 text-xs text-gray-600">(محصولات بدون اجرت)
                    </span>:</label>
                <div class="relative">
                    <input
                        class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="fullname_card_number"
                        type="text"
                        wire:model="data.fullname_card_number"
                    >
                </div>
            </div>
        </div>
    </div>
    {{-- <div class="mt-3">
        <label class="cursor-pointer items-center md:flex">
            <input
                class="peer sr-only"
                type="checkbox"
                wire:model="data.factor"
            >
            <div
                class="peer relative h-6 w-11 shrink-0 rounded-full bg-gray-200 after:absolute after:start-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-red-500 peer-checked:after:-translate-x-full peer-checked:after:border-white peer-focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:peer-focus:ring-red-800 max-md:mb-2 rtl:peer-checked:after:translate-x-full">
            </div>
            <div class="gap-0 max-md:flex max-md:items-center max-md:overflow-hidden">
                <span
                    class="nowrap-ecllipse ms-2 block whitespace-nowrap text-sm font-bold text-gray-900 dark:text-white md:hidden"
                    title="استفاده از پیش فاکتور و سپس ثبت سفارش"
                >استفاده از پیش فاکتور و سپس ثبت سفارش</span>
            </div>
            <div>
                <span
                    class="ms-3 block text-sm font-bold text-gray-900 dark:font-normal dark:text-white max-md:hidden">استفاده
                    از پیش فاکتور و سپس ثبت سفارش</span>
                <span class="ms-3 block text-sm text-gray-900 dark:text-white">اگر این بخش فعال باشد کارشناس سیستم
                    بخش
                    فاکتور را در فروشگاه خواهد دید</span>
            </div>
        </label>
    </div> --}}
    <!-- <div class="mt-3">
        <label class="mb-2 block text-sm font-bold text-gray-700 dark:text-gray-100" for="factorId">شروع شماره فاکتور از:</label>
        <div class="relative">
            <input class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-center text-2xl font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600" id="factorId" type="text" wire:model="factorId">
        </div>
    </div> -->
    <div class="flex flex-row-reverse items-center justify-between max-md:mt-16 md:pt-6">
        <button
            class="mt-3 rounded-xl bg-red-500 px-6 py-3 text-white transition-all hover:bg-red-600 disabled:bg-gray-200 disabled:text-gray-500 max-md:px-3 max-md:py-2"
            type="submit"
        >
            <div class="flex items-center justify-center">
                <svg
                    class="inline h-6 w-6 animate-spin text-red-700"
                    role="status"
                    aria-hidden="true"
                    wire:loading
                    viewBox="0 0 100 101"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                        fill="#E5E7EB"
                    />
                    <path
                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                        fill="currentColor"
                    />
                </svg>
                <span class="mr-3 text-sm max-md:text-sm">ذخیره اطلاعات</span>
            </div>
        </button>
    </div>
</form>

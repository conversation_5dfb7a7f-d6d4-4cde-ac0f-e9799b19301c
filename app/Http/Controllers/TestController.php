<?php

namespace App\Http\Controllers;

use App\Jobs\SendPayamakSurveyJob;
use App\Models\ProductDetail;
use App\Models\Sefaresh;
use App\Models\SefareshAnalysis;
use App\Service\Payamak\SmsService;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use thiagoalessio\TesseractOCR\TesseractOCR;

class TestController extends Controller
{
    public $baseUrlProduction;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        $this->baseUrlProduction = env('APP_TIDAMODE_SHOP_URL', 'https://shop.tidamode.ir/');
    }

    public function index()
    {


        // $tempPath = storage_path('app/public/temp/Untitled.jpeg');

        // if (!file_exists($tempPath)) {
        //     throw new \Exception("Image not found: $tempPath");
        // }

        // $output = (new TesseractOCR($tempPath))
        //     ->lang('fas')
        //     ->run();

        // $persianNumbers = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
        // $englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
        // $output = str_replace($persianNumbers, $englishNumbers, $output);


        // $output = preg_replace("/\r\n?/", "\n", $output);
        // $output = preg_replace('/[ \t]+/', ' ', $output);


        // $lines = array_filter(array_map('trim', explode("\n", $output)), function ($l) {
        //     return $l !== '';
        // });


        // return response()->json(['lines' => array_values($lines)]);




        // $title_fa = "گردنبند ورساچه";

        // $data = DB::table('product_details_view')
        //     ->select('product_title_fa')
        //     ->where('status', 'active')
        //     ->when(!empty($title_fa), function ($query, $title_fa) {
        //         $words = array_filter(explode(' ', trim($title_fa)));

        //         $query->where(function ($q) use ($words) {
        //             foreach ($words as $word) {
        //                 $q->orWhere('product_title_fa', 'LIKE', "%{$word}%");
        //             }
        //         });
        //     })

        //     ->get();

        // dd($data);

        // $chunkSize = 1000;

        // $setting = \App\Models\Setting::whereIn('type', [
        //     'tax',
        // ])->pluck('body', 'type')->toArray();

        // // $tax = isset($setting['tax']) ? floatval($setting['tax']) / 100 : 0.09;

        // $p = ProductDetail::where('out_stock', null)->where('reserved_count', null)->latest()->select(['id', 'weight', 'construction_wages', 'profit', 'tax', 'stone_price'])->chunkById($chunkSize, function ($products) use ($setting) {
        //     foreach ($products as $item) {

        //         $tax = $item->tax != null ? (int) $item->tax / 100 : floatval($setting['tax']) / 100;
        //         $taxProduct = $item->tax != null ? $item->tax : floatval($setting['tax']);

        //         $result = getMoney($item->toArray(), null, true, $tax);


        //         $item->update([
        //             'gold18k' => $result['gold18k'],
        //             'amount' => $result['money'],
        //             'amount_after_offer' => $result['moneyAfterOffer'],
        //             'tax' => $taxProduct,
        //         ]);

        //     }
        // });
        // return $p->count();

        // $chunkSize = 1000;

        // Sefaresh::where('user_id', '94')->chunkById($chunkSize, function ($products) {
        //     foreach ($products as $item) {

        //         $item->survey_sms = 'send';
        //         $item->save();

        //         $link = $this->baseUrlProduction . 'survey/' . hashId($item->id);

        //         $data = [
        //             'fullname' => $item->fullname,
        //             'phone' => $item->phone,
        //             'orderId' => hashId($item->id),
        //             'link' => $link,
        //         ];

        //         SendPayamakSurveyJob::dispatch($data);
        //     }
        // });

        // $smsService = new SmsService;
        // $response = $smsService->send('login', [
        //     'phone' => '09397192230',
        //     'fullname' => 'اسم',
        //     'code' => '5665',
        // ]);

        // return $response;
        // $smsService = new SmsService;
        // $response = $smsService->send('tasvie', [
        //     'phone' => '09397192230',
        //     'fullname' => 'سیدعلی موسوی',
        //     'code' => '123456',
        //     'status' => 'تایید شد',
        // ]);

        // $response = $smsService->send('order_send', [
        //     'phone' => '09397192230',
        //     'code' => 'G2020',
        // ]);

        // $response = $smsService->send('change_order_status', [
        //     'phone' => '09397192230',
        //     'fullname' => 'سیدعلی موسوی',
        //     'code' => '123456',
        //     'status' => 'در حال ارسال',
        // ]);

        // $response = $smsService->send('tracking_post', [
        //     'phone' => '09397192230',
        //     'code' => '321321312312312321',
        // ]);

        // $response = $smsService->send('tracking_tipax', [
        //     'phone' => '09397192230',
        //     'code' => '321321312312312321',
        // ]);

        // dd($response);

        // $chunkSize = 1000;

        // $setting = \App\Models\Setting::whereIn('type', [
        //     'tax',
        // ])->pluck('body', 'type')->toArray();

        // // $tax = isset($setting['tax']) ? floatval($setting['tax']) / 100 : 0.09;

        // ProductDetail::where('out_stock', null)->where('reserved_count', null)->latest()->select(['id', 'weight', 'construction_wages', 'profit', 'tax', 'stone_price'])->chunkById($chunkSize, function ($products) use ($setting) {
        //     foreach ($products as $item) {

        //         $tax = $item->tax != null ? (int) $item->tax / 100 : floatval($setting['tax']) / 100;
        //         $taxProduct = $item->tax != null ? $item->tax : floatval($setting['tax']);

        //         $result = getMoney($item->toArray(), null, true, $tax);

        //         $item->update([
        //             'gold18k' => $result['gold18k'],
        //             'amount' => $result['money'],
        //             'tax' => intval($setting['tax']),
        //         ]);

        //     }
        // });

        // dd('finish products update price');

    }

    public function order_latest()
    {
        $from = Carbon::now()->subDays(3)->startOfDay();
        $to = Carbon::now()->endOfDay();

        $p = Sefaresh::whereBetween('created_at', [$from, $to])->whereNull('survey')->whereNull('survey_sms')->where('last_status', 'send')->first();

        if ($p) {
            $p->survey_sms = 'send';
            $p->save();

            $link = $this->baseUrlProduction . 'survey/' . hashId($p->id);
            $data = [
                'fullname' => 'سیدعلی موسوی',
                'phone' => '09397192230',
                'orderId' => hashId($p->id),
                'link' => $link,
                'id' => $p->id,
            ];

            SendPayamakSurveyJob::dispatch($data);
            dd($data);
        }

        // $chunkSize = 1000;

        // // تعیین بازه زمانی از سه روز پیش تا امروز
        // $from = Carbon::now()->subDays(3)->startOfDay();
        // $to = Carbon::now()->endOfDay();

        // Sefaresh::whereBetween('created_at', [$from, $to])
        //     ->whereNull('survey')
        //     ->where('last_status', 'send')->chunkById($chunkSize, function ($products) {
        //         foreach ($products as $item) {

        //             $link = $this->baseUrlProduction.'survey/'.hashId($item->id);

        //             $data = [
        //                 'fullname' => $item->fullname,
        //                 'phone' => $item->phone,
        //                 'orderId' => hashId($item->id),
        //                 'link' => $link,
        //             ];

        //             dd($data);
        //             // SendPayamakSurveyJob::dispatch($data);
        //         }
        //     });
    }
}

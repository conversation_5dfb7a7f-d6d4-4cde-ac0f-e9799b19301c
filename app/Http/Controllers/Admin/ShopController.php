<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ShopController extends Controller
{
    public function index(){
        return view('dashboard.admin.shop.index');
    }

    public function create(){
        return view('dashboard.admin.shop.create');
    }

    public function show($productId){
        return view('dashboard.admin.shop.show', compact('productId'));
    }

    public function checkout(){
        return view('dashboard.admin.shop.checkout');
    }

    public function transactions(){
        return view('dashboard.admin.shop.transactions.index');
    }

}

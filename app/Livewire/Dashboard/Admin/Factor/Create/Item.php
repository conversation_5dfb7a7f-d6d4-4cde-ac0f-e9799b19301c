<?php

namespace App\Livewire\Dashboard\Admin\Factor\Create;

use App\Models\FactorItemEmpty;
use Livewire\Attributes\On;
use Livewire\Component;

class Item extends Component
{
    public array $data = [
        'code' => null,
        'title' => null,
        'gold18k' => null,
        'cutie' => null,
        'weight' => null,
        'construction_wages' => null,
        'profit' => null,
        'tax' => null,
        'total' => null,
        'eticket' => null,
        'post' => null,
    ];

    public $keyItem;

    public $facorItemId;

    public function saveItem()
    {
        $factorItems = FactorItemEmpty::whereId($this->facorItemId)->first();
        if (isset($factorItems) && $factorItems != null) {
            $factorItems->eticket = $this->data['eticket'];
            $factorItems->code = $this->data['code'];
            $factorItems->title = $this->data['title'];
            $factorItems->gold18k = $this->data['gold18k'];
            $factorItems->cutie = $this->data['cutie'];
            $factorItems->weight = $this->data['weight'];
            $factorItems->construction_wages = $this->data['construction_wages'];
            $factorItems->profit = $this->data['profit'];
            $factorItems->tax = $this->data['tax'];
            $factorItems->total = $this->data['total'];
            $factorItems->save();
        }
    }

    #[On('gold18k')]
    public function updateDataGold18k($gold18k)
    {
        $this->data['gold18k'] = $gold18k;
        $this->calcFactorItem();
    }

    #[On('post')]
    public function updateDataPost($post)
    {
        $this->data['post'] = $post;
        $this->calcFactorItem();
    }

    public function mount()
    {
        dd($this->facorItemId);
        $factorItems = FactorItemEmpty::whereId($this->facorItemId)->first();
        if (isset($factorItems) && $factorItems != null) {
            $this->data['eticket'] = $factorItems->eticket;
            $this->data['code'] = $factorItems->code;
            $this->data['title'] = $factorItems->title;
            $this->data['gold18k'] = $factorItems->gold18k;
            $this->data['cutie'] = $factorItems->cutie;
            $this->data['weight'] = $factorItems->weight;
            $this->data['construction_wages'] = $factorItems->construction_wages;
            $this->data['profit'] = $factorItems->profit;
            $this->data['tax'] = $factorItems->tax;
            $this->data['total'] = $factorItems->total;

        }
    }

    public function calcFactorItem()
    {
        $setting = \App\Models\Setting::whereIn('type', ['tax'])
            ->pluck('body', 'type')
            ->toArray();

        if ($this->data['gold18k'] != null && $this->data['weight'] != null && $this->data['construction_wages'] != null && $this->data['profit'] != null && $this->data['cutie'] != null) {

            $gold18K = str_replace(',', '', $this->data['gold18k']);

            $weight = $this->data['weight'];
            $constructionWagesPercent = $this->data['construction_wages'] / 100;
            $profitPercent = $this->data['profit'] / 100;
            $cutie = $this->data['cutie'];

            // محاسبه اجرت ساخت
            $wage = $weight * (float) $constructionWagesPercent;

            // محاسبه سود
            $profit = ($weight + $wage) * $profitPercent;

            // محاسبه قیمت کل بدون مالیات
            $totalPriceBeforeTax = $weight + $wage + $profit;

            // محاسبه قیمت کل با ضرب در قیمت روز طلا
            $totalPrice = $totalPriceBeforeTax * $gold18K;

            $tax = isset($setting['tax']) ? floatval($setting['tax']) / 100 : 0.09;
            // محاسبه مالیات
            $tax = ($totalPrice - ($weight * $gold18K)) * $tax;

            // محاسبه نهایی
            $finalPrice = $totalPrice + $tax;

            // نمایش نتیجه
            // $result = number_format($finalPrice, 0, '.', ',');
            if ($this->data['post'] != 'yes') {
                $this->data['total'] = number_format($finalPrice, 0, '.', ',');
            }

        } else {
            $this->data['total'] = 0;
        }

        $this->saveItemChange();
    }

    public function saveItemChange()
    {
        $factorItems = FactorItemEmpty::whereId($this->facorItemId)->first();
        if (isset($factorItems) && $factorItems != null) {

            $factorItems->eticket = $this->data['eticket'];
            $factorItems->code = $this->data['code'];
            $factorItems->title = $this->data['title'];
            $factorItems->gold18k = $this->data['gold18k'];
            $factorItems->cutie = $this->data['cutie'];
            $factorItems->weight = $this->data['weight'];
            $factorItems->construction_wages = $this->data['construction_wages'];
            $factorItems->profit = $this->data['profit'];
            $factorItems->tax = $this->data['tax'];
            $factorItems->total = $this->data['total'];

            $factorItems->save();

        }
        $this->dispatch('change-total-factor');
    }

    // public function updatedGold18k(){
    //     $this->calcFactorItem();
    // }

    public function updatedDataWeight()
    {
        // dd($this->gold18k);
        $this->calcFactorItem();
    }

    public function updatedDataConstructionWages()
    {
        $this->calcFactorItem();
    }

    public function updatedDataProfit()
    {
        $this->calcFactorItem();
    }

    public function updatedDataTotal()
    {
        $this->calcFactorItem();
    }

    public function render()
    {
        $this->calcFactorItem();

        return view('livewire.dashboard.admin.factor.create.item');
    }
}

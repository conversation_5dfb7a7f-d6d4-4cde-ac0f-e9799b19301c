<?php

namespace App\Livewire\Dashboard\Admin\Factor\Create;

use App\Models\FactorItem;
use App\Models\FactorItemEmpty;
use App\Models\FactorOrder;
use App\Models\FactorOrderEmpty;
use App\Models\Subscribe;
use Jan<PERSON>nerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\Lazy;
use Livewire\Attributes\On;
use Livewire\Component;

class Index extends Component
{
    use LivewireAlert;

    public $items;

    public $post;

    public $order;

    public $title;

    public $code;

    public $eticket;

    public $phone;

    public $gold18k;

    public $factor_number;

    public $factor_create_date;

    public $factor_total;

    public $factor_deposit;

    public $factor_discount;

    public $total;

    public $user_attach;

    public function mount()
    {

        if ($this->order == null) {

            $this->loadFactorEmpty();

            return;
        }

        $this->loadFactorOrder();

        // $this->loadGold18k();
    }

    private function loadFactorEmpty()
    {
        $factorLatestId = FactorOrder::where('order_id', null)->latest()->first();
        // $this->post = isset($factorLatestId->post) && $factorLatestId != null ? $factorLatestId->post : null;

        $factorItems = FactorItemEmpty::where('order_id', null)->get();
        if ($factorItems->count() == 0) {

            $v = verta();
            $month = strlen($v->month) == 1 ? '0'.$v->month : $v->month;
            $day = strlen($v->day) == 1 ? '0'.$v->day : $v->day;
            $today = $v->year.'/'.$month.'/'.$day;

            $factor = FactorOrderEmpty::create([
                'user_id' => auth()->user()->id,
                'factor_number' => isset($factorLatestId) && $factorLatestId != null ? $factorLatestId->factor_number : 1,
                'factor_create_date' => $today,
            ]);

            for ($i = 0; $i < 5; $i++) {
                FactorItemEmpty::create([
                    'user_id' => auth()->user()->id,
                    'factor_id' => $factor->factor_number,
                    'code' => '',
                    'eticket' => '',
                    'title' => '',
                    'gold18k' => '',
                    'cutie' => '18',
                    'weight' => '0',
                    'construction_wages' => '0',
                    'profit' => '7',
                    'tax' => '10',
                    'total' => '0',
                ]);

            }
        }

        $factorOrderEmpty = FactorOrderEmpty::where('order_id', null)->latest()->first();
        if (isset($factorOrderEmpty) && $factorOrderEmpty != null) {
            $this->title = $factorOrderEmpty->fullname;
            $this->phone = $factorOrderEmpty->phone;
            $this->factor_number = $factorOrderEmpty->factor_number;
            $this->factor_create_date = $factorOrderEmpty->factor_create_date;
            $this->code = $factorOrderEmpty->subscribe_code;
            $this->eticket = $factorOrderEmpty->eticket;
            $this->factor_total = $factorOrderEmpty->factor_total;
            $this->factor_deposit = $factorOrderEmpty->factor_deposit;
            $this->factor_discount = $factorOrderEmpty->factor_discount;
            $this->total = $factorOrderEmpty->total;
            $this->user_attach = $factorOrderEmpty->user_attach;

        }

        $factorItems = FactorItemEmpty::where('order_id', null)->get();
        $this->items = $factorItems;
    }

    private function loadFactorOrder()
    {

        $deposit1 = $this->order->deposit1 != null ? str_replace(',', '', $this->order->deposit1) : 0;
        $deposit2 = $this->order->deposit2 != null ? str_replace(',', '', $this->order->deposit2) : 0;

        $factorLatestId = FactorOrder::where('order_id', $this->order->id)->latest()->first();
        // $this->post = isset($factorLatestId->post) && $factorLatestId != null ? $factorLatestId->post : null;

        // if(isset($factorLatestId) &&  $factorLatestId != null){

        //     $factorItems = FactorItem::where('order_id', $this->order->id)->get();
        //     $this->items = $factorItems;
        // }else{
        $factorItems = FactorItemEmpty::where('order_id', $this->order->id)->get();
        if ($factorItems->count() == 0) {

            $v = verta();
            $month = strlen($v->month) == 1 ? '0'.$v->month : $v->month;
            $day = strlen($v->day) == 1 ? '0'.$v->day : $v->day;
            $today = $v->year.'/'.$month.'/'.$day;

            $factor = FactorOrderEmpty::create([
                'user_id' => auth()->user()->id,
                'order_id' => $this->order->id,
                'factor_deposit' => $deposit1 + $deposit2,
                'factor_discount' => '0',
                'factor_number' => isset($factorLatestId) && $factorLatestId != null ? $factorLatestId->factor_number : 1,
                'factor_create_date' => shamsiDateLimit($this->order->created_at),
            ]);

            FactorItemEmpty::create([
                'user_id' => auth()->user()->id,
                'factor_id' => $factor->factor_number,
                'order_id' => $this->order->id,
                'code' => $this->order->code,
                'eticket' => '',
                'title' => $this->order->name_pluck,
                // 'gold18k' => '',
                'cutie' => '18',
                'weight' => $this->order->gram != null ? $this->order->gram : 0,
                'construction_wages' => '0',
                'profit' => '7',
                'tax' => '10',
                'total' => '0',
            ]);

            for ($i = 0; $i < 5; $i++) {
                FactorItemEmpty::create([
                    'user_id' => auth()->user()->id,
                    'factor_id' => $factor->factor_number,
                    'order_id' => $this->order->id,
                    'code' => '',
                    'title' => '',
                    // 'gold18k' => '',
                    'cutie' => '18',
                    'weight' => '0',
                    'construction_wages' => '0',
                    'profit' => '7',
                    'tax' => '10',
                    'total' => '0',
                ]);

            }
        }

        $factorOrderEmpty = FactorOrderEmpty::where('order_id', $this->order->id)->latest()->first();
        if (isset($factorOrderEmpty) && $factorOrderEmpty != null) {

            $factorOrderEmpty->fullname = $this->order->fullname;
            $factorOrderEmpty->phone = $this->order->phone;
            $user = Subscribe::where('mobile', $this->order->phone)->first();
            if (isset($user) && $user != null) {
                $factorOrderEmpty->subscribe_code = $user->code;
            }
            $factorOrderEmpty->save();

            $factorOrderEmpty = FactorOrderEmpty::where('order_id', $this->order->id)->latest()->first();
            $this->title = $factorOrderEmpty->fullname;
            $this->phone = $factorOrderEmpty->phone;
            $this->factor_number = $factorOrderEmpty->factor_number;
            $this->factor_create_date = $factorOrderEmpty->factor_create_date;
            $this->code = $factorOrderEmpty->subscribe_code;
            $this->eticket = $factorOrderEmpty->eticket;
            $this->factor_total = $factorOrderEmpty->factor_total;
            $this->factor_deposit = $factorOrderEmpty->factor_deposit;
            $this->factor_discount = $factorOrderEmpty->factor_discount;
            $this->total = $factorOrderEmpty->total;
            $this->user_attach = $factorOrderEmpty->user_attach;

        }

        $factorItems = FactorItemEmpty::where('order_id', $this->order->id)->get();
        $this->items = $factorItems;
        // }

    }

    public function acceptGold()
    {
        if ($this->order == null) {
            FactorItemEmpty::where('order_id', null)->update([
                'gold18k' => $this->gold18k,
            ]);
        } else {
            FactorItemEmpty::where('order_id', $this->order->id)->update([
                'gold18k' => $this->gold18k,
            ]);
        }
        $this->dispatch('gold18k', gold18k: $this->gold18k);
    }

    #[Lazy]
    public function loadGold18k()
    {
        $this->gold18k = getGold18k();
        if ($this->order == null) {
            FactorItemEmpty::where('order_id', null)->update([
                'gold18k' => $this->gold18k,
            ]);
        } else {
            FactorItemEmpty::where('order_id', $this->order->id)->update([
                'gold18k' => $this->gold18k,
            ]);
        }
        $this->dispatch('gold18k', gold18k: $this->gold18k);
    }

    #[On('set-user')]
    public function setSubscriber($userId)
    {
        if ($this->order == null) {
            $user = Subscribe::whereId($userId)->first();

            if (isset($user) && $user != null) {
                $this->title = $user->fullname;
                $this->phone = $user->mobile;
                $this->code = $user->code;
            }

            $factorOrderEmpty = FactorOrderEmpty::latest()->first();
            if (isset($factorOrderEmpty) && $factorOrderEmpty != null) {
                $factorOrderEmpty->fullname = $user->fullname;
                $factorOrderEmpty->phone = $user->mobile;
                $factorOrderEmpty->subscribe_code = $user->code;
                $factorOrderEmpty->subscribe_id = $user->id;
                $factorOrderEmpty->save();
            }
        } else {
            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'امکان ویرایش اطلاعات مشتری از این بخش امکان پذیر نیست، بایستی در فرم ویرایش این اطلاعات اصلاح گردد',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);
        }

    }

    public function render()
    {

        return view('livewire.dashboard.admin.factor.create.index');
    }
}

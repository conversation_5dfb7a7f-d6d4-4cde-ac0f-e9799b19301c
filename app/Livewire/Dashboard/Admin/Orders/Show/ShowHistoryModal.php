<?php

namespace App\Livewire\Dashboard\Admin\Orders\Show;

use Livewire\Component;
use App\Models\Order;
use App\Models\StatusHistory;
class ShowHistoryModal extends Component
{
    public $order;

    public function render()
    {
        return view('livewire.dashboard.admin.orders.show.show-history-modal',[
            'historys' => StatusHistory::where('sefaresh_id', $this->order->id)->get()
        ]);
    }
}

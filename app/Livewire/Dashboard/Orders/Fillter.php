<?php

namespace App\Livewire\Dashboard\Orders;

use App\Models\Sefaresh;
use Livewire\Attributes\On;
use Livewire\Component;

class Fillter extends Component
{
    public array $data = [
        'code' => null,
        'mobile' => null,
        'address' => null,
        'MonthlyOne' => null,
        'MonthlyTow' => null,
        'Yearly' => null,
        'category' => null,
        'type_construction' => null,
        'sex' => null,
        'fullname' => null,
        'phone' => null,
        'whatsapp' => null,
        'date_last_status' => null,
        'name_pluck' => null,
        'model' => null,
        'chain' => null,
        'user_id' => null,
        'financial' => null,
        'code_order' => null,
        'last_status' => null,
        'postType' => null,
        'factor' => null,
        'count' => 0,
        'eticket' => null,
    ];

    public $orders = [];

    protected $queryString = ['data.code', 'data.fullname', 'data.mobile', 'data.address'];

    public function mount()
    {
        $this->data['MonthlyOne'] = verta()->month;
        $this->data['MonthlyTow'] = verta()->month;
        $this->data['Yearly'] = verta()->year;
    }

    #[On('orders-count-notread')]
    public function getCount($count)
    {
        $this->data['count'] = $count;
    }

    public function ClearFillter()
    {
        // Reset all properties to null
        $this->resetDataProperties();

        // Dispatch the event
        $this->dispatch('order-filter', null);
    }

    public function fillter()
    {

        // If all filters are null, we can return early
        if ($this->isAllDataNull()) {
            $this->dispatch('order-filter', ['query' => null]);

            return;
        }

        // Build the query based on the filters
        $query = $this->buildQuery();

        // Execute and get the count
        $this->data['count'] = $query->count();

        // Get the query SQL and bindings
        $sqlQuery = $query->toSql();
        $bindings = $query->getBindings();

        // Compress the query and send it
        $compressedQuery = json_encode(['sql' => $sqlQuery, 'bindings' => $bindings]);

        dd($compressedQuery);
        $this->dispatch('order-filter', ['query' => $compressedQuery]);
    }

    private function resetDataProperties()
    {
        // Reset all the filter properties
        $properties = array_keys($this->data);
        foreach ($properties as $property) {
            $this->data[$property] = null;
        }
    }

    private function isAllDataNull()
    {
        // Check if all data properties are null
        foreach ($this->data as $value) {
            if (! is_null($value)) {
                return false;
            }
        }

        return true;
    }

    private function buildQuery()
    {
        // Start with the base query
        $query = Sefaresh::query();

        // Add filters based on the data
        $filters = [
            'category', 'type_construction', 'sex', 'color', 'fullname', 'phone', 'whatsapp',
            'date_last_status', 'name_pluck', 'model', 'chain', 'user_id', 'financial',
            'code_order', 'last_status', 'postType',
        ];

        foreach ($filters as $filter) {
            if (! is_null($this->data[$filter])) {
                $query->where($filter, 'LIKE', '%'.$this->data[$filter].'%');
            }
        }

        // Add month and year filters
        $query->whereBetween('month', [$this->data['MonthlyOne'], $this->data['MonthlyTow']])
            ->where('year', 'LIKE', $this->data['Yearly']);

        // Handle special factor filter
        if ($this->data['factor'] === 'null') {
            $query->where('whatsapp', '');
        } else {
            $query->where('whatsapp', '!=', null);
        }

        // Add user-specific filter for non-admins
        if (auth()->user()->level !== 'admin') {
            $query->where('user_id', auth()->user()->id);
        }

        $this->data['count'] = $query->count();

        return $query;
    }

    public function render()
    {
        return view('livewire.dashboard.orders.fillter');
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Sefaresh extends Model
{
    protected $fillable = [
        'invoice_id', 'chat_id', 'user_id', 'code', 'type', 'type_construction', 'model', 'sex', 'color', 'font', 'name_pluck', 'post_type',
        'tracking_code', 'tips', 'total_amount', 'deposit1', 'deposit2', 'remaining', 'package_type',
        'package_amount', 'fullname', 'phone', 'address', 'codepost', 'chain', 'size', 'size_wrist', 'size_ankle',
        'order_register_date', 'customer_date', 'last_status', 'date_last_status', 'image1', 'image2',
        'image3', 'image4', 'image5', 'image6', 'image7', 'year', 'month', 'day', 'whatsapp', 'phone_whatsapp', 'product_code', 'read', 'designer_id', 'manufacturer_id', 'dimensions', 'sefaresh_total',
        'financial', 'bookmark', 'checkmark', 'gram', 'gold18k', 'checked', 'order_type', 'fullname_agent', 'phone_agent', 'survey_sms', 'survey',
    ];

    public function factor()
    {
        return $this->hasMany(FactorItemEmpty::class, 'order_id', 'id');
    }

    public function invoice()
    {
        return $this->belongsTo(Invoice::class, 'invoice_id', 'id');
    }

    public function factorTotal()
    {
        return $this->hasOne(FactorOrderEmpty::class, 'order_id', 'id')->latest();
    }

    public function market()
    {
        return $this->hasOne(Market::class, 'sefaresh_id');
    }

    public function manufacturer()
    {
        return $this->hasOne(Setting::class, 'id', 'manufacturer_id');
    }

    public function designer()
    {
        return $this->hasOne(Setting::class, 'id', 'designer_id');
    }

    public function resiver()
    {
        return $this->hasOne(Resiver::class, 'sefaresh_id');
    }

    public function financial()
    {
        return $this->hasOne(Financial::class, 'sefaresh_id');
    }

    public function subscribe()
    {
        return $this->hasOne(Subscribe::class, 'mobile', 'phone');
    }

    public function scopeCurrentUser($query)
    {
        return $query->where('user_id', auth()->user()->id);
    }

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function userRecipient()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function iType()
    {
        switch ($this->type) {
            case 'jewelry':
                return 'گردنبند';
            case 'bracelet':
                return 'دستبند';
            case 'ring':
                return 'انگشتر';
            case 'spoon':
                return 'ست';
            case 'ankle_jewelry':
                return 'پابند';
            case 'earring':
                return 'گوشواره';
            default:
                return '-';
        }
    }

    public function iLastStatus()
    {
        switch ($this->last_status) {
            case 'design':
                return ['message' => 'در حال طراحی', 'style' => 'bg-yellow-200 text-yellow-700'];
            case 'wait_design':
                return ['message' => 'منتظر انتخاب طرح', 'style' => 'bg-red-500 text-white'];
            case 'cut':
                return ['message' => 'فایل برش', 'style' => 'bg-blue-600 text-white'];
            case 'ready_to_build':
                return ['message' => 'آماده به ساخت', 'style' => 'bg-gray-100 text-gray-700'];
            case 'wait_factory':
                return ['message' => 'در حال ساخت', 'style' => 'bg-gray-200 text-gray-700'];
            case 'ready':
                return ['message' => 'آماده به ارسال', 'style' => 'bg-gray-800 text-white'];
            case 'ready-on':
                return ['message' => 'درحال ارسال', 'style' => 'bg-sky-500 text-sky-900'];
            case 'money':
                return ['message' => 'منتظر تسویه مشتری', 'style' => 'bg-gray-200 text-gray-700'];
            case 'send':
                return ['message' => 'ارسال شد', 'style' => 'bg-green-500 text-white'];
            case 'cancel':
                return ['message' => 'کنسل', 'style' => 'bg-red-500 text-white'];
            case 'created':
                return ['message' => 'ساخته شد', 'style' => 'bg-green-500 text-white'];
            default:
                return ['message' => '-', 'style' => '-'];
        }
    }

    public function factors()
    {
        return $this->hasOne(FactorOrderEmpty::class, 'order_id', 'id');
    }

    public function surveyRelated()
    {
        return $this->hasOne(SurveyResponse::class, 'survey_id', 'id');
    }

    public function transaction()
    {
        return $this->hasOne(ZinbalTransaction::class, 'orderId', 'id');
    }

    public function surveyResponses()
    {
        return $this->hasMany(SurveyResponse::class, 'survey_id', 'id');
    }

    public function orders()
    {
        return $this->hasMany(Sefaresh::class, 'phone', 'phone');
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FactorItemEmpty extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'factor_id',
        'order_id',
        'code',
        'eticket',
        'title',
        'gold18k',
        'cutie',
        'weight',
        'sender',
        'packing',
        'construction_wages',
        'profit',
        'tax',
        'post',
        'total',
        'product_id',
    ];

    public function gallery()
    {
        return $this->hasMany(ProductGallery::class, 'product_id', 'product_id');
    }
}
